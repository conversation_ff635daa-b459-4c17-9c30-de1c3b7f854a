/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import * as nls from '../../../../nls.js';
import { basename } from '../../../../base/common/resources.js';
import { MarkerSeverity } from '../../../../platform/markers/common/markers.js';
export default class Messages {
    static { this.MARKERS_PANEL_TOGGLE_LABEL = nls.localize('problems.view.toggle.label', "Toggle Problems (Errors, Warnings, Infos)"); }
    static { this.MARKERS_PANEL_SHOW_LABEL = nls.localize2('problems.view.focus.label', "Focus Problems (Errors, Warnings, Infos)"); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_TITLE = nls.localize('problems.panel.configuration.title', "Problems View"); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_AUTO_REVEAL = nls.localize('problems.panel.configuration.autoreveal', "Controls whether Problems view should automatically reveal files when opening them."); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_VIEW_MODE = nls.localize('problems.panel.configuration.viewMode', "Controls the default view mode of the Problems view."); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_SHOW_CURRENT_STATUS = nls.localize('problems.panel.configuration.showCurrentInStatus', "When enabled shows the current problem in the status bar."); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_COMPARE_ORDER = nls.localize('problems.panel.configuration.compareOrder', "Controls the order in which problems are navigated."); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_COMPARE_ORDER_SEVERITY = nls.localize('problems.panel.configuration.compareOrder.severity', "Navigate problems ordered by severity"); }
    static { this.PROBLEMS_PANEL_CONFIGURATION_COMPARE_ORDER_POSITION = nls.localize('problems.panel.configuration.compareOrder.position', "Navigate problems ordered by position"); }
    static { this.MARKERS_PANEL_TITLE_PROBLEMS = nls.localize2('markers.panel.title.problems', "Problems"); }
    static { this.MARKERS_PANEL_NO_PROBLEMS_BUILT = nls.localize('markers.panel.no.problems.build', "No problems have been detected in the workspace."); }
    static { this.MARKERS_PANEL_NO_PROBLEMS_ACTIVE_FILE_BUILT = nls.localize('markers.panel.no.problems.activeFile.build', "No problems have been detected in the current file."); }
    static { this.MARKERS_PANEL_NO_PROBLEMS_FILTERS = nls.localize('markers.panel.no.problems.filters', "No results found with provided filter criteria."); }
    static { this.MARKERS_PANEL_ACTION_TOOLTIP_MORE_FILTERS = nls.localize('markers.panel.action.moreFilters', "More Filters..."); }
    static { this.MARKERS_PANEL_FILTER_LABEL_SHOW_ERRORS = nls.localize('markers.panel.filter.showErrors', "Show Errors"); }
    static { this.MARKERS_PANEL_FILTER_LABEL_SHOW_WARNINGS = nls.localize('markers.panel.filter.showWarnings', "Show Warnings"); }
    static { this.MARKERS_PANEL_FILTER_LABEL_SHOW_INFOS = nls.localize('markers.panel.filter.showInfos', "Show Infos"); }
    static { this.MARKERS_PANEL_FILTER_LABEL_EXCLUDED_FILES = nls.localize('markers.panel.filter.useFilesExclude', "Hide Excluded Files"); }
    static { this.MARKERS_PANEL_FILTER_LABEL_ACTIVE_FILE = nls.localize('markers.panel.filter.activeFile', "Show Active File Only"); }
    static { this.MARKERS_PANEL_ACTION_TOOLTIP_FILTER = nls.localize('markers.panel.action.filter', "Filter Problems"); }
    static { this.MARKERS_PANEL_ACTION_TOOLTIP_QUICKFIX = nls.localize('markers.panel.action.quickfix', "Show fixes"); }
    static { this.MARKERS_PANEL_FILTER_ARIA_LABEL = nls.localize('markers.panel.filter.ariaLabel', "Filter Problems"); }
    static { this.MARKERS_PANEL_FILTER_PLACEHOLDER = nls.localize('markers.panel.filter.placeholder', "Filter (e.g. text, **/*.ts, !**/node_modules/**)"); }
    static { this.MARKERS_PANEL_FILTER_ERRORS = nls.localize('markers.panel.filter.errors', "errors"); }
    static { this.MARKERS_PANEL_FILTER_WARNINGS = nls.localize('markers.panel.filter.warnings', "warnings"); }
    static { this.MARKERS_PANEL_FILTER_INFOS = nls.localize('markers.panel.filter.infos', "infos"); }
    static { this.MARKERS_PANEL_SINGLE_ERROR_LABEL = nls.localize('markers.panel.single.error.label', "1 Error"); }
    static { this.MARKERS_PANEL_MULTIPLE_ERRORS_LABEL = (noOfErrors) => { return nls.localize('markers.panel.multiple.errors.label', "{0} Errors", '' + noOfErrors); }; }
    static { this.MARKERS_PANEL_SINGLE_WARNING_LABEL = nls.localize('markers.panel.single.warning.label', "1 Warning"); }
    static { this.MARKERS_PANEL_MULTIPLE_WARNINGS_LABEL = (noOfWarnings) => { return nls.localize('markers.panel.multiple.warnings.label', "{0} Warnings", '' + noOfWarnings); }; }
    static { this.MARKERS_PANEL_SINGLE_INFO_LABEL = nls.localize('markers.panel.single.info.label', "1 Info"); }
    static { this.MARKERS_PANEL_MULTIPLE_INFOS_LABEL = (noOfInfos) => { return nls.localize('markers.panel.multiple.infos.label', "{0} Infos", '' + noOfInfos); }; }
    static { this.MARKERS_PANEL_SINGLE_UNKNOWN_LABEL = nls.localize('markers.panel.single.unknown.label', "1 Unknown"); }
    static { this.MARKERS_PANEL_MULTIPLE_UNKNOWNS_LABEL = (noOfUnknowns) => { return nls.localize('markers.panel.multiple.unknowns.label', "{0} Unknowns", '' + noOfUnknowns); }; }
    static { this.MARKERS_PANEL_AT_LINE_COL_NUMBER = (ln, col) => { return nls.localize('markers.panel.at.ln.col.number', "[Ln {0}, Col {1}]", '' + ln, '' + col); }; }
    static { this.MARKERS_TREE_ARIA_LABEL_RESOURCE = (noOfProblems, fileName, folder) => { return nls.localize('problems.tree.aria.label.resource', "{0} problems in file {1} of folder {2}", noOfProblems, fileName, folder); }; }
    static { this.MARKERS_TREE_ARIA_LABEL_MARKER = (marker) => {
        const relatedInformationMessage = marker.relatedInformation.length ? nls.localize('problems.tree.aria.label.marker.relatedInformation', " This problem has references to {0} locations.", marker.relatedInformation.length) : '';
        switch (marker.marker.severity) {
            case MarkerSeverity.Error:
                return marker.marker.source ? nls.localize('problems.tree.aria.label.error.marker', "Error: {0} at line {1} and character {2}.{3} generated by {4}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage, marker.marker.source)
                    : nls.localize('problems.tree.aria.label.error.marker.nosource', "Error: {0} at line {1} and character {2}.{3}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage);
            case MarkerSeverity.Warning:
                return marker.marker.source ? nls.localize('problems.tree.aria.label.warning.marker', "Warning: {0} at line {1} and character {2}.{3} generated by {4}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage, marker.marker.source)
                    : nls.localize('problems.tree.aria.label.warning.marker.nosource', "Warning: {0} at line {1} and character {2}.{3}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage, relatedInformationMessage);
            case MarkerSeverity.Info:
                return marker.marker.source ? nls.localize('problems.tree.aria.label.info.marker', "Info: {0} at line {1} and character {2}.{3} generated by {4}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage, marker.marker.source)
                    : nls.localize('problems.tree.aria.label.info.marker.nosource', "Info: {0} at line {1} and character {2}.{3}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage);
            default:
                return marker.marker.source ? nls.localize('problems.tree.aria.label.marker', "Problem: {0} at line {1} and character {2}.{3} generated by {4}", marker.marker.source, marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage, marker.marker.source)
                    : nls.localize('problems.tree.aria.label.marker.nosource', "Problem: {0} at line {1} and character {2}.{3}", marker.marker.message, marker.marker.startLineNumber, marker.marker.startColumn, relatedInformationMessage);
        }
    }; }
    static { this.MARKERS_TREE_ARIA_LABEL_RELATED_INFORMATION = (relatedInformation) => nls.localize('problems.tree.aria.label.relatedinfo.message', "{0} at line {1} and character {2} in {3}", relatedInformation.message, relatedInformation.startLineNumber, relatedInformation.startColumn, basename(relatedInformation.resource)); }
    static { this.SHOW_ERRORS_WARNINGS_ACTION_LABEL = nls.localize('errors.warnings.show.label', "Show Errors and Warnings"); }
}
//# sourceMappingURL=data:application/json;base64,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