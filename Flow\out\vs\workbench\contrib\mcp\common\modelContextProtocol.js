/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
/* eslint-disable @stylistic/ts/member-delimiter-style */
/* eslint-disable local/code-no-unexternalized-strings */
/**
 * Schema updated from the Model Context Protocol repository at
 * https://github.com/modelcontextprotocol/specification/tree/main/schema
 *
 * ⚠️ Do not edit within `namespace` manually except to update schema versions ⚠️
 */
export var MCP;
(function (MCP) {
    MCP.LATEST_PROTOCOL_VERSION = "2024-11-05";
    MCP.JSONRPC_VERSION = "2.0";
    // Standard JSON-RPC error codes
    MCP.PARSE_ERROR = -32700;
    MCP.INVALID_REQUEST = -32600;
    MCP.METHOD_NOT_FOUND = -32601;
    MCP.INVALID_PARAMS = -32602;
    MCP.INTERNAL_ERROR = -32603;
})(MCP || (MCP = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibW9kZWxDb250ZXh0UHJvdG9jb2wuanMiLCJzb3VyY2VSb290IjoiZmlsZTovLy9DOi9Vc2Vycy9CaGF3ZXNoL0Rlc2t0b3AvdGVzdGluZ19wdXJwb3Nlcy90ZXN0aW5nX3B1cnBvc2VzL0Zsb3cvc3JjLyIsInNvdXJjZXMiOlsidnMvd29ya2JlbmNoL2NvbnRyaWIvbWNwL2NvbW1vbi9tb2RlbENvbnRleHRQcm90b2NvbC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7O2dHQUdnRztBQUVoRyx5REFBeUQ7QUFDekQseURBQXlEO0FBRXpEOzs7OztHQUtHO0FBQ0gsTUFBTSxLQUFXLEdBQUcsQ0FtbUNuQjtBQW5tQ0QsV0FBaUIsR0FBRztJQVFOLDJCQUF1QixHQUFHLFlBQVksQ0FBQztJQUN2QyxtQkFBZSxHQUFHLEtBQUssQ0FBQztJQXlFckMsZ0NBQWdDO0lBQ25CLGVBQVcsR0FBRyxDQUFDLEtBQUssQ0FBQztJQUNyQixtQkFBZSxHQUFHLENBQUMsS0FBSyxDQUFDO0lBQ3pCLG9CQUFnQixHQUFHLENBQUMsS0FBSyxDQUFDO0lBQzFCLGtCQUFjLEdBQUcsQ0FBQyxLQUFLLENBQUM7SUFDeEIsa0JBQWMsR0FBRyxDQUFDLEtBQUssQ0FBQztBQTRnQ3RDLENBQUMsRUFubUNnQixHQUFHLEtBQUgsR0FBRyxRQW1tQ25CIn0=