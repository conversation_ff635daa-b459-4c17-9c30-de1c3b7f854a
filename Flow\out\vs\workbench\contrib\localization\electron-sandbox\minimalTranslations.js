/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { localize } from '../../../../nls.js';
// The strings localized in this file will get pulled into the manifest of the language packs.
// So that they are available for VS Code to use without downloading the entire language pack.
export const minimumTranslatedStrings = {
    showLanguagePackExtensions: localize('showLanguagePackExtensions', "Search language packs in the Marketplace to change the display language to {0}."),
    searchMarketplace: localize('searchMarketplace', "Search Marketplace"),
    installAndRestartMessage: localize('installAndRestartMessage', "Install language pack to change the display language to {0}."),
    installAndRestart: localize('installAndRestart', "Install and Restart")
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWluaW1hbFRyYW5zbGF0aW9ucy5qcyIsInNvdXJjZVJvb3QiOiJmaWxlOi8vL0M6L1VzZXJzL0JoYXdlc2gvRGVza3RvcC90ZXN0aW5nX3B1cnBvc2VzL3Rlc3RpbmdfcHVycG9zZXMvRmxvdy9zcmMvIiwic291cmNlcyI6WyJ2cy93b3JrYmVuY2gvY29udHJpYi9sb2NhbGl6YXRpb24vZWxlY3Ryb24tc2FuZGJveC9taW5pbWFsVHJhbnNsYXRpb25zLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Z0dBR2dHO0FBRWhHLE9BQU8sRUFBRSxRQUFRLEVBQUUsTUFBTSxvQkFBb0IsQ0FBQztBQUU5Qyw4RkFBOEY7QUFDOUYsOEZBQThGO0FBRTlGLE1BQU0sQ0FBQyxNQUFNLHdCQUF3QixHQUE4QjtJQUNsRSwwQkFBMEIsRUFBRSxRQUFRLENBQUMsNEJBQTRCLEVBQUUsaUZBQWlGLENBQUM7SUFDckosaUJBQWlCLEVBQUUsUUFBUSxDQUFDLG1CQUFtQixFQUFFLG9CQUFvQixDQUFDO0lBQ3RFLHdCQUF3QixFQUFFLFFBQVEsQ0FBQywwQkFBMEIsRUFBRSw4REFBOEQsQ0FBQztJQUM5SCxpQkFBaUIsRUFBRSxRQUFRLENBQUMsbUJBQW1CLEVBQUUscUJBQXFCLENBQUM7Q0FDdkUsQ0FBQyJ9