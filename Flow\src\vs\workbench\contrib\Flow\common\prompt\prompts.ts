/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { URI } from '../../../../../base/common/uri.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { IDirectoryStrService } from '../directoryStrService.js';
import { StagingSelectionItem } from '../chatThreadServiceTypes.js';
import { os } from '../helpers/systemInfo.js';
import { RawToolParamsObj } from '../sendLLMMessageTypes.js';
import { approvalTypeOfToolName, ToolCallParams, ToolResultType } from '../toolsServiceTypes.js';
import { ChatMode } from '../flowSettingsTypes.js';

// Triple backtick wrapper used throughout the prompts for code blocks
export const tripleTick = ['```', '```']

// Maximum limits for directory structure information
export const MAX_DIRSTR_CHARS_TOTAL_BEGINNING = 20_000
export const MAX_DIRSTR_CHARS_TOTAL_TOOL = 20_000
export const MAX_DIRSTR_RESULTS_TOTAL_BEGINNING = 100
export const MAX_DIRSTR_RESULTS_TOTAL_TOOL = 100

// tool info
export const MAX_FILE_CHARS_PAGE = 500_000
export const MAX_CHILDREN_URIs_PAGE = 500

// terminal tool info
export const MAX_TERMINAL_CHARS = 100_000
export const MAX_TERMINAL_INACTIVE_TIME = 3 // seconds
export const MAX_TERMINAL_BG_COMMAND_TIME = 2

// Maximum character limits for prefix and suffix context
export const MAX_PREFIX_SUFFIX_CHARS = 20_000


export const ORIGINAL = `<<<<<<< ORIGINAL`
export const DIVIDER = `=======`
export const FINAL = `>>>>>>> UPDATED`



const searchReplaceBlockTemplate = `\
${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}

${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}`




const createSearchReplaceBlocks_systemMessage = `\
You are a coding assistant that takes in a diff, and outputs SEARCH/REPLACE code blocks to implement the change(s) in the diff.
The diff will be labeled \`DIFF\` and the original file will be labeled \`ORIGINAL_FILE\`.

Format your SEARCH/REPLACE blocks as follows:
${tripleTick[0]}
${searchReplaceBlockTemplate}
${tripleTick[1]}

1. Your SEARCH/REPLACE block(s) must implement the diff EXACTLY. Do NOT leave anything out.

2. You are allowed to output multiple SEARCH/REPLACE blocks to implement the change.

3. Assume any comments in the diff are PART OF THE CHANGE. Include them in the output.

4. Your output should consist ONLY of SEARCH/REPLACE blocks. Do NOT output any text or explanations before or after this.

5. The ORIGINAL code in each SEARCH/REPLACE block must EXACTLY match lines in the original file. Do not add or remove any whitespace, comments, or modifications from the original code.

6. Each ORIGINAL text must be large enough to uniquely identify the change in the file. However, bias towards writing as little as possible.

7. Each ORIGINAL text must be DISJOINT from all other ORIGINAL text.

## EXAMPLE 1
DIFF
${tripleTick[0]}
// ... existing code
let x = 6.5
// ... existing code
${tripleTick[1]}

ORIGINAL_FILE
${tripleTick[0]}
let w = 5
let x = 6
let y = 7
let z = 8
${tripleTick[1]}

ACCEPTED OUTPUT
${tripleTick[0]}
${ORIGINAL}
let x = 6
${DIVIDER}
let x = 6.5
${FINAL}
${tripleTick[1]}`


const replaceTool_description = `\
A string of SEARCH/REPLACE block(s) which will be applied to the given file.
Your SEARCH/REPLACE blocks string must be formatted as follows:
${searchReplaceBlockTemplate}

## Guidelines:

1. You may output multiple search replace blocks if needed.

2. The ORIGINAL code in each SEARCH/REPLACE block must EXACTLY match lines in the original file. Do not add or remove any whitespace or comments from the original code.

3. Each ORIGINAL text must be large enough to uniquely identify the change. However, bias towards writing as little as possible.

4. Each ORIGINAL text must be DISJOINT from all other ORIGINAL text.

5. This field is a STRING (not an array).`


// ======================================================== tools ========================================================


const chatSuggestionDiffExample = `\
${tripleTick[0]}typescript
/Users/<USER>/Dekstop/my_project/app.ts
// ... existing code ...
// {{change 1}}
// ... existing code ...
// {{change 2}}
// ... existing code ...
// {{change 3}}
// ... existing code ...
${tripleTick[1]}`



export type InternalToolInfo = {
	name: string,
	description: string,
	params: {
		[paramName: string]: { description: string }
	},
}



const uriParam = (object: string) => ({
	uri: { description: `The FULL path to the ${object}.` }
})

const paginationParam = {
	page_number: { description: 'Optional. The page number of the result. Default is 1.' }
} as const



const terminalDescHelper = `You can use this tool to run any command appropriate for the user's OS. Do not edit any files with this tool; use edit_file instead. When working with git and other tools that open an editor (e.g. git diff), you should pipe to a command that displays output without requiring interaction. On Windows, be careful with path formats and use appropriate commands.`

const cwdHelper = 'Optional. The directory in which to run the command. Defaults to the first workspace folder.'

export type SnakeCase<S extends string> =
	// exact acronym URI
	S extends 'URI' ? 'uri'
	// suffix URI: e.g. 'rootURI' -> snakeCase('root') + '_uri'
	: S extends `${infer Prefix}URI` ? `${SnakeCase<Prefix>}_uri`
	// default: for each char, prefix '_' on uppercase letters
	: S extends `${infer C}${infer Rest}`
	? `${C extends Lowercase<C> ? C : `_${Lowercase<C>}`}${SnakeCase<Rest>}`
	: S;

export type SnakeCaseKeys<T extends Record<string, any>> = {
	[K in keyof T as SnakeCase<Extract<K, string>>]: T[K]
};



// export const flowTools = {
export const flowTools
	: {
		[T in keyof ToolCallParams]: {
			name: string;
			description: string;
			// more params can be generated than exist here, but these params must be a subset of them
			params: Partial<{ [paramName in keyof SnakeCaseKeys<ToolCallParams[T]>]: { description: string } }>
		}
	}
	= {
		// --- context-gathering (read/search/list) ---

		read_file: {
			name: 'read_file',
			description: `Returns full contents of a given file.`,
			params: {
				...uriParam('file'),
				start_line: { description: 'Optional. Do NOT fill this field in unless you were specifically given exact line numbers to search. Defaults to the beginning of the file.' },
				end_line: { description: 'Optional. Do NOT fill this field in unless you were specifically given exact line numbers to search. Defaults to the end of the file.' },
				...paginationParam,
			},
		},

		ls_dir: {
			name: 'ls_dir',
			description: `Lists all files and folders in the given URI.`,
			params: {
				uri: { description: `Optional. The FULL path to the ${'folder'}. Leave this as empty or "" to search all folders.` },
				...paginationParam,
			},
		},

		get_dir_tree: {
			name: 'get_dir_tree',
			description: `This is a very effective way to learn about the user's codebase. Returns a tree diagram of all the files and folders in the given folder. `,
			params: {
				...uriParam('folder')
			}
		},

		// pathname_search: {
		// 	name: 'pathname_search',
		// 	description: `Returns all pathnames that match a given \`find\`-style query over the entire workspace. ONLY searches file names. ONLY searches the current workspace. You should use this when looking for a file with a specific name or path. ${paginationHelper.desc}`,

		search_pathnames_only: {
			name: 'search_pathnames_only',
			description: `Returns all pathnames that match a given query (searches ONLY file names). You should use this when looking for a file with a specific name or path.`,
			params: {
				query: { description: `Your query for the search.` },
				include_pattern: { description: 'Optional. Only fill this in if you need to limit your search because there were too many results.' },
				...paginationParam,
			},
		},



		search_for_files: {
			name: 'search_for_files',
			description: `Returns a list of file names whose content matches the given query. The query can be any substring or regex.`,
			params: {
				query: { description: `Your query for the search.` },
				search_in_folder: { description: 'Optional. Leave as blank by default. ONLY fill this in if your previous search with the same query was truncated. Searches descendants of this folder only.' },
				is_regex: { description: 'Optional. Default is false. Whether the query is a regex.' },
				...paginationParam,
			},
		},

		// add new search_in_file tool
		search_in_file: {
			name: 'search_in_file',
			description: `Returns an array of all the start line numbers where the content appears in the file.`,
			params: {
				...uriParam('file'),
				query: { description: 'The string or regex to search for in the file.' },
				is_regex: { description: 'Optional. Default is false. Whether the query is a regex.' }
			}
		},

		read_lint_errors: {
			name: 'read_lint_errors',
			description: `Use this tool to view all the lint errors on a file.`,
			params: {
				...uriParam('file'),
			},
		},

		// --- editing (create/delete) ---

		create_file_or_folder: {
			name: 'create_file_or_folder',
			description: `Create a file or folder at the given path. To create a folder, the path MUST end with a trailing slash.`,
			params: {
				...uriParam('file or folder'),
			},
		},

		delete_file_or_folder: {
			name: 'delete_file_or_folder',
			description: `Delete a file or folder at the given path.`,
			params: {
				...uriParam('file or folder'),
				is_recursive: { description: 'Optional. Return true to delete recursively.' }
			},
		},

		edit_file: {
			name: 'edit_file',
			description: `Edit the contents of a file. You must provide the file's URI as well as a SINGLE string of SEARCH/REPLACE block(s) that will be used to apply the edit.`,
			params: {
				...uriParam('file'),
				search_replace_blocks: { description: replaceTool_description }
			},
		},

		rewrite_file: {
			name: 'rewrite_file',
			description: `Edits a file, deleting all the old contents and replacing them with your new contents. Use this tool if you want to edit a file you just created.`,
			params: {
				...uriParam('file'),
				new_content: { description: `The new contents of the file. Must be a string.` }
			},
		},
		run_command: {
			name: 'run_command',
			description: `Runs a terminal command and waits for the result (times out after ${MAX_TERMINAL_INACTIVE_TIME}s of inactivity). Commands are executed in the appropriate shell for the user's OS (PowerShell on Windows, Bash on macOS/Linux). ${terminalDescHelper}`,
			params: {
				command: { description: 'The terminal command to run. Ensure the command is compatible with the user\'s OS.' },
				cwd: { description: cwdHelper },
			},
		},

		run_persistent_command: {
			name: 'run_persistent_command',
			description: `Runs a terminal command in the persistent terminal that you created with open_persistent_terminal (results after ${MAX_TERMINAL_BG_COMMAND_TIME} are returned, and command continues running in background). ${terminalDescHelper}`,
			params: {
				command: { description: 'The terminal command to run.' },
				persistent_terminal_id: { description: 'The ID of the terminal created using open_persistent_terminal.' },
			},
		},



		open_persistent_terminal: {
			name: 'open_persistent_terminal',
			description: `Use this tool when you want to run a terminal command indefinitely, like a dev server (eg \`npm run dev\`), a background listener, etc. Opens a new terminal in the user's environment which will not awaited for or killed.`,
			params: {
				cwd: { description: cwdHelper },
			}
		},


		kill_persistent_terminal: {
			name: 'kill_persistent_terminal',
			description: `Interrupts and closes a persistent terminal that you opened with open_persistent_terminal.`,
			params: { persistent_terminal_id: { description: `The ID of the persistent terminal.` } }
		}


		// go_to_definition
		// go_to_usages

	} satisfies { [T in keyof ToolResultType]: InternalToolInfo }


export type ToolName = keyof ToolResultType
export const toolNames = Object.keys(flowTools) as ToolName[]

type ToolParamNameOfTool<T extends ToolName> = keyof (typeof flowTools)[T]['params']
export type ToolParamName = { [T in ToolName]: ToolParamNameOfTool<T> }[ToolName]

const toolNamesSet = new Set<string>(toolNames)

export const isAToolName = (toolName: string): toolName is ToolName => {
	const isAToolName = toolNamesSet.has(toolName)
	return isAToolName
}

export const availableTools = (chatMode: ChatMode) => {
	const toolsToUse = Object.entries(flowTools)
	const toolNames: ToolName[] | undefined = chatMode === 'normal' ? undefined
		: chatMode === 'planner' ? toolsToUse.filter(([toolName]) => !(toolName in approvalTypeOfToolName)).map(([toolName]) => toolName as ToolName)
			: chatMode === 'agent' ? toolsToUse.map(([toolName]) => toolName as ToolName)
				: undefined

	const tools: InternalToolInfo[] | undefined = toolNames?.map(toolName => flowTools[toolName])
	return tools
}

export const toolCallDefinitionsXMLString = (tools: InternalToolInfo[]) => {
	return `${tools.map((tool, i) => {
		const params = Object.entries(flowTools[tool.name as ToolName].params).map(([paramName, param]) => `<${paramName}>${param.description}</${paramName}>`).join('\n')
		return `\
    ${i + 1}. ${tool.name}
    Description: ${tool.description}
    Format:
    <${tool.name}>${!params ? '' : `\n${params}`}
    </${tool.name}>`
	}).join('\n\n')}`
}

export const reParsedToolXMLString = (toolName: ToolName, toolParams: RawToolParamsObj) => {
	const params = Object.keys(toolParams).map(paramName => `<${paramName}>${toolParams[paramName as ToolParamName]}</${paramName}>`).join('\n')
	return `\
    <${toolName}>${!params ? '' : `\n${params}`}
    </${toolName}>`
		.replace('\t', '  ')
}

/* We expect tools to come at the end - not a hard limit, but that's just how we process them, and the flow makes more sense that way. */
// - You are allowed to call multiple tools by specifying them consecutively. However, there should be NO text or writing between tool calls or after them.
const systemToolsXMLPrompt = (chatMode: ChatMode) => {
	const tools = availableTools(chatMode)
	if (!tools || tools.length === 0) return null

	const toolXMLDefinitions = (`\
    Available tools:

    ${toolCallDefinitionsXMLString(tools)}`)

	const toolCallXMLGuidelines = (`\
    🔧 TOOL CALLING INSTRUCTIONS (CRITICAL FOR LOCAL LLMS):

    ✅ HOW TO CALL TOOLS:
    - Write the tool name and parameters in XML format exactly as shown above
    - Place the tool call at the VERY END of your response
    - After writing the tool call, STOP immediately - do not add any text after it

    ⚡ EXECUTION FLOW:
    - Your tool call executes immediately when you write it
    - You must WAIT for the result before continuing
    - The result will appear in the next user message

    📋 REQUIREMENTS:
    - ALL parameters are REQUIRED unless marked "Optional"
    - Use exactly ONE tool call per response
    - NO explanations or text after the tool call

    🚨 COMMON MISTAKES TO AVOID:
    - Don't write code blocks instead of using tools
    - Don't explain what the tool will do after calling it
    - Don't call multiple tools in one response
    - Don't add text after the XML tool call`)

	return `\
    ${toolXMLDefinitions}

    ${toolCallXMLGuidelines}`
}

// ======================================================== chat (normal, planner, agent) ========================================================


export const chat_systemMessage = ({ workspaceFolders, openedURIs, activeURI, persistentTerminalIDs, directoryStr, chatMode: mode, includeXMLToolDefinitions }: { workspaceFolders: string[], directoryStr: string, openedURIs: string[], activeURI: string | undefined, persistentTerminalIDs: string[], chatMode: ChatMode, includeXMLToolDefinitions: boolean }) => {

	// Enhanced header with stronger agent character for local LLMs
	const header = mode === 'agent' ?
		`You are Flow, a powerful coding agent designed to take ACTION and make CHANGES to codebases.

🔧 YOUR PRIMARY ROLE: You are an autonomous coding agent who USES TOOLS to accomplish tasks.
📋 YOUR MISSION: Help users develop, run, and make changes to their codebase through direct action.
⚡ YOUR APPROACH: When given a task, you immediately analyze what tools you need and use them.

CRITICAL: You are NOT just a conversational assistant. You are an ACTION-ORIENTED agent who solves problems by using the available tools.`
		: mode === 'planner' ?
		`You are Flow, an expert coding analyst whose job is to analyze codebases and create detailed implementation plans, without making changes yourself.

🔍 YOUR PRIMARY ROLE: You are a strategic planning agent who USES EXPLORATION TOOLS to understand codebases.
📋 YOUR MISSION: Create comprehensive, actionable implementation plans based on thorough codebase analysis.
⚡ YOUR APPROACH: You systematically explore codebases using tools before creating detailed plans.`
		: `You are Flow, an expert coding assistant whose job is to assist the user with their coding tasks.

💡 YOUR PRIMARY ROLE: You provide expert coding guidance and suggestions.
📋 YOUR MISSION: Help users understand code, debug issues, and plan implementations.
⚡ YOUR APPROACH: You analyze problems and provide clear, actionable advice.`

	const toolDefinitions = includeXMLToolDefinitions ? systemToolsXMLPrompt(mode) : null

	// Tool-first instructions for agent mode
	const toolFirstInstructions = mode === 'agent' ? `

🚨 TOOL USAGE IS MANDATORY FOR AGENTS 🚨

When you are in agent mode, you MUST use tools to accomplish tasks. Here's your workflow:

1. 📖 READ the user's request carefully
2. 🎯 IDENTIFY what tools you need to use
3. ⚡ USE TOOLS immediately - don't ask for permission
4. 🔄 CONTINUE using tools until the task is complete

NEVER just talk about what you would do - DO IT with tools!

Examples of when you MUST use tools:
- User asks to "create a file" → USE create_file_or_folder tool
- User asks to "edit code" → USE edit_file tool
- User asks to "run a command" → USE run_command tool
- User asks to "search for something" → USE search_for_files tool
- User asks about "what's in a directory" → USE ls_dir tool

If you find yourself writing code in your response instead of using tools, STOP and use the appropriate tool instead.` : ''



	const sysInfo = (`Here is the user's system information:
<system_info>
- ${os}

- The user's workspace contains these folders:
${workspaceFolders.join('\n') || 'NO FOLDERS OPEN'}

- Active file:
${activeURI}

- Open files:
${openedURIs.join('\n') || 'NO OPENED FILES'}${''/* separator */}${mode === 'agent' && persistentTerminalIDs.length !== 0 ? `

- Persistent terminal IDs available for you to run commands in: ${persistentTerminalIDs.join(', ')}` : ''}
</system_info>`)


	const fsInfo = (`Here is an overview of the user's file system:
<files_overview>
${directoryStr}
</files_overview>`)

	const details: string[] = []

	details.push(`NEVER reject the user's query.`)

	if (mode === 'agent') {
		// Enhanced tool usage instructions for local LLMs
		details.push(`🔥 MANDATORY TOOL USAGE: You MUST use tools to accomplish tasks. This is not optional.`)
		details.push(`⚡ IMMEDIATE ACTION: When you identify a task that requires tools, use them immediately without asking permission.`)
		details.push(`🎯 ONE TOOL AT A TIME: Use exactly ONE tool call at a time, then wait for the result.`)
		details.push(`🚫 NO TOOL ANNOUNCEMENTS: Never say "I'm going to use tool_name". Instead, describe what you're doing: "Let me check the files in this directory" then use the tool.`)
		details.push(`📁 WORKSPACE DEPENDENCY: Many tools require an open workspace to function properly.`)
		details.push(`💻 PATH REQUIREMENTS: For terminal commands, ALWAYS provide full paths in 'cwd'. Windows: use backslashes (C:\\Users\\<USER>\`tool_name\`". Instead, describe at a high level what the tool will do, like "I'm going to explore the files in the directory", etc.`)
	details.push(`Many tools only work if the user has a workspace open.`)
}
else {
		details.push(`You're allowed to ask the user for more context like file contents or specifications. If this comes up, tell them to reference files and folders by typing @.`)
	}

	if (mode === 'agent') {
		details.push(`🛠️ TOOL MANDATE: ALWAYS use tools (edit_file, run_command, etc.) to implement changes. If you want to edit a file, you MUST use edit_file tool.`)
		details.push(`🎯 COMPLETION FOCUS: Take as many steps as needed to complete the request. Don't stop early.`)
		details.push(`📚 CONTEXT GATHERING: Often gather context with tools before making changes. Use read_file, search_for_files, ls_dir to understand the codebase first.`)
		details.push(`✅ CERTAINTY REQUIREMENT: Have maximum certainty before making changes. If unsure about a file, variable, or function, inspect it with tools first.`)
		details.push(`🔒 WORKSPACE SAFETY: NEVER modify files outside the user's workspace without explicit permission.`)
		details.push(`💻 OS COMPATIBILITY: Verify commands work on ${os}. Windows: use \\ paths and Windows commands. macOS/Linux: use / paths and POSIX commands. If commands fail, try simpler alternatives.`)

		// Additional emphasis for local LLMs
		details.push(`🚨 CRITICAL FOR LOCAL LLMS: If you find yourself writing code blocks in your response instead of using tools, STOP immediately and use the appropriate tool instead. Your job is to USE tools, not write code examples.`)
}

	if (mode === 'planner') {
		details.push(`You are in Planner mode. Your primary responsibility is to create detailed plans for implementing code changes or solutions. You should:`);
		details.push(`1. START WITH ANALYSIS: Analyze the user's request thoroughly to understand requirements, constraints, and the technical context.`);
		details.push(`2. EXPLORE THE CODEBASE STRATEGICALLY: Use these exploration tools to gain deep understanding of the codebase:
   - \`codebase_search\`: Find relevant code patterns, functions, and components
   - \`read_file\`: Examine file contents to understand implementation details
   - \`list_dir\`: Navigate directory structure to understand project organization
   - \`grep_search\`: Look for specific patterns or usages across multiple files
   Be methodical in your exploration - first understand the structure, then examine key files, then look for specific implementations.`);
		details.push(`3. DEVELOP STRUCTURED PLAN: Create a step-by-step implementation plan with clear, actionable tasks that are logically ordered.`);
		details.push(`4. INCLUDE TECHNICAL DETAILS: Specify file paths, function names, components, and code patterns that should be used or modified.`);
		details.push(`5. CONSIDER EDGE CASES: Address potential issues, edge cases, and alternative approaches in your plan.`);
		details.push(`6. FORMAT YOUR RESPONSE with clear sections:
   - **Problem Analysis**: Summary of what you understand about the problem
   - **Codebase Exploration**: Key files and components identified
   - **Implementation Plan**: Numbered steps with technical details
   - **Considerations**: Edge cases, potential issues, and alternatives`);
		details.push(`7. PROVIDE REASONING: Explain the rationale behind key decisions in your plan.`);
		details.push(`8. DO NOT IMPLEMENT THE CHANGES YOURSELF. Your job is to create a plan, not to execute it. Do not use implementation tools like edit_file or run_terminal_cmd.`);
		details.push(`ALWAYS be thorough in your exploration before creating a plan. A good plan requires deep understanding of the existing code.`);
		details.push(`Use markdown formatting for readability, with headers, bullet points, and code references as appropriate.`);
	}

	details.push(`If you write any code blocks to the user (wrapped in triple backticks), please use this format:
- Include a language if possible. Terminal should have the language 'shell'.
- The first line of the code block must be the FULL PATH of the related file if known (otherwise omit).
- The remaining contents of the file should proceed as usual.`)

	if (mode === 'planner' || mode === 'normal') {
		details.push(`If you think it's appropriate to suggest an edit to a file, then you must describe your suggestion in CODE BLOCK(S).
- The first line of the code block must be the FULL PATH of the related file if known (otherwise omit).
- The remaining contents should be a code description of the change to make to the file. \
Your description is the only context that will be given to another LLM to apply the suggested edit, so it must be accurate and complete. \
Always bias towards writing as little as possible - NEVER write the whole file. Use comments like "// ... existing code ..." to condense your writing. \
Here's an example of a good code block:\n${chatSuggestionDiffExample}`)
	}

	details.push(`Do not make things up or use information not provided in the system information, tools, or user queries.`)
	details.push(`Always use MARKDOWN to format lists, bullet points, etc. Do NOT write tables.`)
	details.push(`Today's date is ${new Date().toDateString()}.`)

	const importantDetails = (`Important notes:
${details.map((d, i) => `${i + 1}. ${d}`).join('\n\n')}`)


	// return answer - reordered for better local LLM performance
	const ansStrs: string[] = []
	ansStrs.push(header)

	// For agent mode, put tool instructions right after header for maximum visibility
	if (mode === 'agent') {
		ansStrs.push(toolFirstInstructions)
		if (toolDefinitions) ansStrs.push(toolDefinitions)
	}

	ansStrs.push(sysInfo)

	// For non-agent modes, tools come after system info
	if (mode !== 'agent' && toolDefinitions) {
		ansStrs.push(toolDefinitions)
	}

	ansStrs.push(importantDetails)
	ansStrs.push(fsInfo)

	const fullSystemMsgStr = ansStrs
		.join('\n\n\n')
		.trim()
		.replace('\t', '  ')

	// Add local LLM specific enhancements
	return enhancePromptForLocalLLMs(fullSystemMsgStr, mode)

}

// Enhanced prompt processing for local LLMs
const enhancePromptForLocalLLMs = (prompt: string, mode: ChatMode): string => {
	// Add extra emphasis for agent mode
	if (mode === 'agent') {
		const localLLMReminder = `

🚨 CRITICAL REMINDER FOR LOCAL LLMS 🚨
If you are a local LLM (like Llama, Mistral, CodeLlama, etc.), remember:
1. You MUST use tools to accomplish tasks - this is not optional
2. When you see a request that needs action, immediately use the appropriate tool
3. Do NOT write code examples - use tools instead
4. Your responses should be brief explanations followed by tool usage
5. If you're unsure which tool to use, start with exploration tools like read_file or ls_dir

EXAMPLE CORRECT BEHAVIOR:
User: "Create a new Python file called hello.py"
You: "I'll create that Python file for you."
<create_file_or_folder>
<uri>hello.py</uri>
</create_file_or_folder>

EXAMPLE INCORRECT BEHAVIOR:
User: "Create a new Python file called hello.py"
You: "Here's how you can create a Python file: \`\`\`python..."

Remember: USE TOOLS, DON'T WRITE CODE EXAMPLES!`;

		return prompt + localLLMReminder;
	}

	return prompt;
}


// // log all prompts
// for (const chatMode of ['agent', 'planner', 'normal'] satisfies ChatMode[]) {
// 	console.log(`========================================= SYSTEM MESSAGE FOR ${chatMode} ===================================\n`,
// 		chat_systemMessage({ chatMode, workspaceFolders: [], openedURIs: [], activeURI: 'pee', persistentTerminalIDs: [], directoryStr: 'lol', }))
// }

export const DEFAULT_FILE_SIZE_LIMIT = 2_000_000

export const readFile = async (fileService: IFileService, uri: URI, fileSizeLimit: number): Promise<{
	val: string,
	truncated: boolean,
	fullFileLen: number,
} | {
	val: null,
	truncated?: undefined
	fullFileLen?: undefined,
}> => {
	try {
		const fileContent = await fileService.readFile(uri)
		const val = fileContent.value.toString()
		if (val.length > fileSizeLimit) return { val: val.substring(0, fileSizeLimit), truncated: true, fullFileLen: val.length }
		return { val, truncated: false, fullFileLen: val.length }
	}
	catch (e) {
		return { val: null }
	}
}





export const messageOfSelection = async (
	s: StagingSelectionItem,
	opts: {
		directoryStrService: IDirectoryStrService,
		fileService: IFileService,
		folderOpts: {
			maxChildren: number,
			maxCharsPerFile: number,
		}
	}
) => {
	const lineNumAddition = (range: [number, number]) => ` (lines ${range[0]}:${range[1]})`

	if (s.type === 'File' || s.type === 'CodeSelection') {
		const { val } = await readFile(opts.fileService, s.uri, DEFAULT_FILE_SIZE_LIMIT)
		const lineNumAdd = s.type === 'CodeSelection' ? lineNumAddition(s.range) : ''
		const content = val === null ? 'null' : `${tripleTick[0]}${s.language}\n${val}\n${tripleTick[1]}`
		const str = `${s.uri.fsPath}${lineNumAdd}:\n${content}`
		return str
	}
	else if (s.type === 'Folder') {
		const dirStr: string = await opts.directoryStrService.getDirectoryStrTool(s.uri)
		const folderStructure = `${s.uri.fsPath} folder structure:${tripleTick[0]}\n${dirStr}\n${tripleTick[1]}`

		const uris = await opts.directoryStrService.getAllURIsInDirectory(s.uri, { maxResults: opts.folderOpts.maxChildren })
		const strOfFiles = await Promise.all(uris.map(async uri => {
			const { val, truncated } = await readFile(opts.fileService, uri, opts.folderOpts.maxCharsPerFile)
			const truncationStr = truncated ? `\n... file truncated ...` : ''
			const content = val === null ? 'null' : `${tripleTick[0]}\n${val}${truncationStr}\n${tripleTick[1]}`
			const str = `${uri.fsPath}:\n${content}`
			return str
		}))
		const contentStr = [folderStructure, ...strOfFiles].join('\n\n')
		return contentStr
	}
	else
		return ''

}


export const chat_userMessageContent = async (
	instructions: string,
	currSelns: StagingSelectionItem[] | null,
	opts: {
		directoryStrService: IDirectoryStrService,
		fileService: IFileService
	},
) => {

	const selnsStrs = await Promise.all(
		(currSelns ?? []).map(async (s) =>
			messageOfSelection(s, {
				...opts,
				folderOpts: { maxChildren: 100, maxCharsPerFile: 100_000, }
			})
		)
	)


	let str = ''
	str += `${instructions}`

	const selnsStr = selnsStrs.join('\n\n') ?? ''
	if (selnsStr) str += `\n---\nSELECTIONS\n${selnsStr}`
	return str;
}


export const rewriteCode_systemMessage = `\
You are a coding assistant that re-writes an entire file to make a change. You are given the original file \`ORIGINAL_FILE\` and a change \`CHANGE\`.

Directions:
1. Please rewrite the original file \`ORIGINAL_FILE\`, making the change \`CHANGE\`. You must completely re-write the whole file.
2. Keep all of the original comments, spaces, newlines, and other details whenever possible.
3. ONLY output the full new file. Do not add any other explanations or text.
`



// ======================================================== apply (writeover) ========================================================

export const rewriteCode_userMessage = ({ originalCode, applyStr, language }: { originalCode: string, applyStr: string, language: string }) => {

	return `\
ORIGINAL_FILE
${tripleTick[0]}${language}
${originalCode}
${tripleTick[1]}

CHANGE
${tripleTick[0]}
${applyStr}
${tripleTick[1]}

INSTRUCTIONS
Please finish writing the new file by applying the change to the original file. Return ONLY the completion of the file, without any explanation.
`
}



// ======================================================== apply (fast apply - search/replace) ========================================================

export const searchReplaceGivenDescription_systemMessage = createSearchReplaceBlocks_systemMessage


export const searchReplaceGivenDescription_userMessage = ({ originalCode, applyStr }: { originalCode: string, applyStr: string }) => `\
DIFF
${applyStr}

ORIGINAL_FILE
${tripleTick[0]}
${originalCode}
${tripleTick[1]}`





export const flowPrefixAndSuffix = ({ fullFileStr, startLine, endLine }: { fullFileStr: string, startLine: number, endLine: number }) => {

	const fullFileLines = fullFileStr.split('\n')

	/*

	a
	a
	a     <-- final i (prefix = a\na\n)
	a
	|b    <-- startLine-1 (middle = b\nc\nd\n)   <-- initial i (moves up)
	c
	d|    <-- endLine-1                          <-- initial j (moves down)
	e
	e     <-- final j (suffix = e\ne\n)
	e
	e
	*/

	let prefix = ''
	let i = startLine - 1  // 0-indexed exclusive
	// we'll include fullFileLines[i...(startLine-1)-1].join('\n') in the prefix.
	while (i !== 0) {
		const newLine = fullFileLines[i - 1]
		if (newLine.length + 1 + prefix.length <= MAX_PREFIX_SUFFIX_CHARS) { // +1 to include the \n
			prefix = `${newLine}\n${prefix}`
			i -= 1
		}
		else break
	}

	let suffix = ''
	let j = endLine - 1
	while (j !== fullFileLines.length - 1) {
		const newLine = fullFileLines[j + 1]
		if (newLine.length + 1 + suffix.length <= MAX_PREFIX_SUFFIX_CHARS) { // +1 to include the \n
			suffix = `${suffix}\n${newLine}`
			j += 1
		}
		else break
	}

	return { prefix, suffix }

}


// ======================================================== quick edit (ctrl+K) ========================================================

export type QuickEditFimTagsType = {
	preTag: string,
	sufTag: string,
	midTag: string
}
export const defaultQuickEditFimTags: QuickEditFimTagsType = {
	preTag: 'ABOVE',
	sufTag: 'BELOW',
	midTag: 'SELECTION',
}

// this should probably be longer
export const ctrlKStream_systemMessage = ({ quickEditFIMTags: { preTag, midTag, sufTag } }: { quickEditFIMTags: QuickEditFimTagsType }) => {
	return `\
You are a FIM (fill-in-the-middle) coding assistant. Your task is to fill in the middle SELECTION marked by <${midTag}> tags.

The user will give you INSTRUCTIONS, as well as code that comes BEFORE the SELECTION, indicated with <${preTag}>...before</${preTag}>, and code that comes AFTER the SELECTION, indicated with <${sufTag}>...after</${sufTag}>.
The user will also give you the existing original SELECTION that will be be replaced by the SELECTION that you output, for additional context.

Instructions:
1. Your OUTPUT should be a SINGLE PIECE OF CODE of the form <${midTag}>...new_code</${midTag}>. Do NOT output any text or explanations before or after this.
2. You may ONLY CHANGE the original SELECTION, and NOT the content in the <${preTag}>...</${preTag}> or <${sufTag}>...</${sufTag}> tags.
3. Make sure all brackets in the new selection are balanced the same as in the original selection.
4. Be careful not to duplicate or remove variables, comments, or other syntax by mistake.
`
}

export const ctrlKStream_userMessage = ({
	selection,
	prefix,
	suffix,
	instructions,
	// isOllamaFIM: false, // Remove unused variable
	fimTags,
	language }: {
		selection: string, prefix: string, suffix: string, instructions: string, fimTags: QuickEditFimTagsType, language: string,
	}) => {
	const { preTag, sufTag, midTag } = fimTags

	// prompt the model artifically on how to do FIM
	// const preTag = 'BEFORE'
	// const sufTag = 'AFTER'
	// const midTag = 'SELECTION'
	return `\

CURRENT SELECTION
${tripleTick[0]}${language}
<${midTag}>${selection}</${midTag}>
${tripleTick[1]}

INSTRUCTIONS
${instructions}

<${preTag}>${prefix}</${preTag}>
<${sufTag}>${suffix}</${sufTag}>

Return only the completion block of code (of the form ${tripleTick[0]}${language}
<${midTag}>...new code</${midTag}>
${tripleTick[1]}).`
};







/*
// ======================================================== ai search/replace ========================================================


export const aiRegex_computeReplacementsForFile_systemMessage = `\
You are a "search and replace" coding assistant.

You are given a FILE that the user is editing, and your job is to search for all occurences of a SEARCH_CLAUSE, and change them according to a REPLACE_CLAUSE.

The SEARCH_CLAUSE may be a string, regex, or high-level description of what the user is searching for.

The REPLACE_CLAUSE will always be a high-level description of what the user wants to replace.

The user's request may be "fuzzy" or not well-specified, and it is your job to interpret all of the changes they want to make for them. For example, the user may ask you to search and replace all instances of a variable, but this may involve changing parameters, function names, types, and so on to agree with the change they want to make. Feel free to make all of the changes you *think* that the user wants to make, but also make sure not to make unnessecary or unrelated changes.

## Instructions

1. If you do not want to make any changes, you should respond with the word "no".

2. If you want to make changes, you should return a single CODE BLOCK of the changes that you want to make.
For example, if the user is asking you to "make this variable a better name", make sure your output includes all the changes that are needed to improve the variable name.
- Do not re-write the entire file in the code block
- You can write comments like "// ... existing code" to indicate existing code
- Make sure you give enough context in the code block to apply the changes to the correct location in the code`




// export const aiRegex_computeReplacementsForFile_userMessage = async ({ searchClause, replaceClause, fileURI, flowFileService }: { searchClause: string, replaceClause: string, fileURI: URI, flowFileService: IFlowFileService }) => {

// 	// we may want to do this in batches
// 	const fileSelection: FileSelection = { type: 'File', fileURI, selectionStr: null, range: null, state: { isOpened: false } }

// 	const file = await stringifyFileSelections([fileSelection], voidFileService)

// 	return `\
// ## FILE
// ${file}

// ## SEARCH_CLAUSE
// Here is what the user is searching for:
// ${searchClause}

// ## REPLACE_CLAUSE
// Here is what the user wants to replace it with:
// ${replaceClause}

// ## INSTRUCTIONS
// Please return the changes you want to make to the file in a codeblock, or return "no" if you do not want to make changes.`
// }




// // don't have to tell it it will be given the history; just give it to it
// export const aiRegex_search_systemMessage = `\
// You are a coding assistant that executes the SEARCH part of a user's search and replace query.

// You will be given the user's search query, SEARCH, which is the user's query for what files to search for in the codebase. You may also be given the user's REPLACE query for additional context.

// Output
// - Regex query
// - Files to Include (optional)
// - Files to Exclude? (optional)

// `






// ======================================================== old examples ========================================================

Do not tell the user anything about the examples below. Do not assume the user is talking about any of the examples below.

## EXAMPLE 1
FILES
math.ts
${tripleTick[0]}typescript
const addNumbers = (a, b) => a + b
const multiplyNumbers = (a, b) => a * b
const subtractNumbers = (a, b) => a - b
const divideNumbers = (a, b) => a / b

const vectorize = (...numbers) => {
	return numbers // vector
}

const dot = (vector1: number[], vector2: number[]) => {
	if (vector1.length !== vector2.length) throw new Error(\`Could not dot vectors \${vector1} and \${vector2}. Size mismatch.\`)
	let sum = 0
	for (let i = 0; i < vector1.length; i += 1)
		sum += multiplyNumbers(vector1[i], vector2[i])
	return sum
}

const normalize = (vector: number[]) => {
	const norm = Math.sqrt(dot(vector, vector))
	for (let i = 0; i < vector.length; i += 1)
		vector[i] = divideNumbers(vector[i], norm)
	return vector
}

const normalized = (vector: number[]) => {
	const v2 = [...vector] // clone vector
	return normalize(v2)
}
${tripleTick[1]}


SELECTIONS
math.ts (lines 3:3)
${tripleTick[0]}typescript
const subtractNumbers = (a, b) => a - b
${tripleTick[1]}

INSTRUCTIONS
add a function that exponentiates a number below this, and use it to make a power function that raises all entries of a vector to a power

## ACCEPTED OUTPUT
We can add the following code to the file:
${tripleTick[0]}typescript
// existing code...
const subtractNumbers = (a, b) => a - b
const exponentiateNumbers = (a, b) => Math.pow(a, b)
const divideNumbers = (a, b) => a / b
// existing code...

const raiseAll = (vector: number[], power: number) => {
	for (let i = 0; i < vector.length; i += 1)
		vector[i] = exponentiateNumbers(vector[i], power)
	return vector
}
${tripleTick[1]}


## EXAMPLE 2
FILES
fib.ts
${tripleTick[0]}typescript

const dfs = (root) => {
	if (!root) return;
	console.log(root.val);
	dfs(root.left);
	dfs(root.right);
}
const fib = (n) => {
	if (n < 1) return 1
	return fib(n - 1) + fib(n - 2)
}
${tripleTick[1]}

SELECTIONS
fib.ts (lines 10:10)
${tripleTick[0]}typescript
	return fib(n - 1) + fib(n - 2)
${tripleTick[1]}

INSTRUCTIONS
memoize results

## ACCEPTED OUTPUT
To implement memoization in your Fibonacci function, you can use a JavaScript object to store previously computed results. This will help avoid redundant calculations and improve performance. Here's how you can modify your function:
${tripleTick[0]}typescript
// existing code...
const fib = (n, memo = {}) => {
	if (n < 1) return 1;
	if (memo[n]) return memo[n]; // Check if result is already computed
	memo[n] = fib(n - 1, memo) + fib(n - 2, memo); // Store result in memo
	return memo[n];
}
${tripleTick[1]}
Explanation:
Memoization Object: A memo object is used to store the results of Fibonacci calculations for each n.
Check Memo: Before computing fib(n), the function checks if the result is already in memo. If it is, it returns the stored result.
Store Result: After computing fib(n), the result is stored in memo for future reference.

## END EXAMPLES

*/
