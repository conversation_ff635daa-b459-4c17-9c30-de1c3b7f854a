/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { OnText, OnFinalMessage } from '../../common/sendLLMMessageTypes.js';
import { ChatMode } from '../../common/flowSettingsTypes.js';

export interface ToolUsageEnforcerOptions {
	modelName: string;
	chatMode: ChatMode | null;
	isSmallModel: boolean;
}

export const createToolUsageEnforcer = (
	onText: OnText,
	onFinalMessage: OnFinalMessage,
	options: ToolUsageEnforcerOptions
): { newOnText: OnText; newOnFinalMessage: OnFinalMessage } => {
	const { modelName, chatMode, isSmallModel } = options;

	// Only enforce for agent mode and small models
	if (chatMode !== 'agent' || !isSmallModel) {
		return { newOnText: onText, newOnFinalMessage: onFinalMessage };
	}

	let codeBlockDetectionCount = 0;
	let lastCodeBlockWarning = 0;
	const maxCodeBlockWarnings = 3;
	const warningCooldown = 5000; // 5 seconds

	const newOnText: OnText = (params) => {
		// Detect code blocks that should be tool calls
		const codeBlockPattern = /```(\w+)?\n([\s\S]*?)```/g;
		const codeBlocks = Array.from(params.fullText.matchAll(codeBlockPattern));

		if (codeBlocks.length > 0 && !params.toolCall) {
			codeBlockDetectionCount++;
			const now = Date.now();

			if (now - lastCodeBlockWarning > warningCooldown && codeBlockDetectionCount <= maxCodeBlockWarnings) {
				lastCodeBlockWarning = now;

				for (const match of codeBlocks) {
					const language = match[1] || 'unknown';
					const code = match[2];

					// Detect file creation scenarios
					if (isFileCreationCode(language, code)) {
						console.warn(`[Tool Enforcer] ${modelName} should use create_file_or_folder tool for ${language} files`);
						console.warn(`[Tool Enforcer] Correct format: <create_file_or_folder><uri>path/to/file.${getFileExtension(language)}</uri></create_file_or_folder>`);
					}

					// Detect command scenarios
					if (isCommandCode(language, code)) {
						console.warn(`[Tool Enforcer] ${modelName} should use run_command tool for commands`);
						console.warn(`[Tool Enforcer] Correct format: <run_command><command>your_command</command></run_command>`);
						console.warn(`[Tool Enforcer] Optional: Add <cwd>working_directory</cwd> if needed`);
					}

					// Detect file editing scenarios
					if (isFileEditCode(language, code)) {
						console.warn(`[Tool Enforcer] ${modelName} should use edit_file tool for file modifications`);
						console.warn(`[Tool Enforcer] Correct format: <edit_file><uri>path/to/file</uri><search_replace_blocks>search_replace_content</search_replace_blocks></edit_file>`);
					}
				}
			}
		}

		onText(params);
	};

	const newOnFinalMessage: OnFinalMessage = (params) => {
		// Final check and summary
		if (codeBlockDetectionCount > 0) {
			console.warn(`[Tool Enforcer] Summary for ${modelName}: Detected ${codeBlockDetectionCount} code blocks that should have been tool calls`);
			console.warn(`[Tool Enforcer] Reminder: Use XML tools like <create_file_or_folder>, <run_command>, <edit_file> instead of code blocks`);
		}

		onFinalMessage(params);
	};

	return { newOnText, newOnFinalMessage };
};

// Helper functions to detect different types of code that should be tool calls
function isFileCreationCode(language: string, code: string): boolean {
	const fileLanguages = ['html', 'css', 'javascript', 'typescript', 'python', 'java', 'cpp', 'c', 'go', 'rust', 'php', 'ruby'];
	return fileLanguages.includes(language.toLowerCase()) && code.trim().length > 50;
}

function isCommandCode(language: string, code: string): boolean {
	const commandLanguages = ['shell', 'bash', 'cmd', 'powershell', 'terminal'];
	const commandPatterns = [
		/^npm\s+/m, /^yarn\s+/m, /^pnpm\s+/m,
		/^cd\s+/m, /^mkdir\s+/m, /^touch\s+/m,
		/^git\s+/m, /^docker\s+/m, /^pip\s+/m,
		/^node\s+/m, /^python\s+/m, /^java\s+/m
	];

	return commandLanguages.includes(language.toLowerCase()) ||
		commandPatterns.some(pattern => pattern.test(code));
}

function isFileEditCode(language: string, code: string): boolean {
	// Detect code that looks like it's modifying existing files
	const editIndicators = [
		/\/\/\s*.*existing.*code/i,
		/\/\*\s*.*existing.*code/i,
		/\#\s*.*existing.*code/i,
		/\/\/\s*.*add.*this/i,
		/\/\/\s*.*replace.*this/i,
		/\/\/\s*.*modify.*this/i
	];

	return editIndicators.some(pattern => pattern.test(code));
}

function getFileExtension(language: string): string {
	const extensions: { [key: string]: string } = {
		'html': 'html',
		'css': 'css',
		'javascript': 'js',
		'typescript': 'ts',
		'python': 'py',
		'java': 'java',
		'cpp': 'cpp',
		'c': 'c',
		'go': 'go',
		'rust': 'rs',
		'php': 'php',
		'ruby': 'rb'
	};

	return extensions[language.toLowerCase()] || 'txt';
}
